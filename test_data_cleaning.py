#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据清理功能
"""

import pandas as pd
from data_processor import DataValidator


def test_quantity_cleaning():
    """测试单数数据清理功能"""
    print("测试数据清理功能")
    print("=" * 50)
    
    # 测试数据
    test_cases = [
        # (输入值, 期望的是否有效, 期望的清理后值)
        ("123", True, 123),
        ("45.6", True, 46),  # 四舍五入
        ("100个", True, 100),
        ("￥50", True, 50),
        ("$30.5", True, 30),  # 修正期望值
        ("", True, 0),
        (None, True, 0),
        ("abc", True, 0),
        ("12.34元", True, 12),
        ("-10", True, 0),  # 负数转为0
        ("1.23E+02", True, 123),  # 科学计数法
        ("   25   ", True, 25),  # 带空格
        ("零", True, 0),  # 中文
        ("12,345", True, 12345),  # 带逗号
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (input_value, expected_valid, expected_value) in enumerate(test_cases, 1):
        try:
            is_valid, cleaned_value = DataValidator.clean_and_validate_quantity(input_value)
            
            print(f"测试 {i:2d}: '{input_value}' -> 有效:{is_valid}, 值:{cleaned_value}")
            
            if is_valid == expected_valid and cleaned_value == expected_value:
                print(f"         ✅ 通过")
                success_count += 1
            else:
                print(f"         ❌ 失败 (期望: 有效:{expected_valid}, 值:{expected_value})")
                
        except Exception as e:
            print(f"测试 {i:2d}: '{input_value}' -> ❌ 异常: {e}")
        
        print()
    
    print(f"测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count


def test_dataframe_cleaning():
    """测试DataFrame数据清理"""
    print("\n测试DataFrame数据清理")
    print("=" * 50)
    
    # 创建测试DataFrame
    test_data = {
        "搜索关键词": ["关键词1", "关键词2", "关键词3", "", None, "关键词4"],
        "单数": ["123", "45.6个", "￥100", "", None, "abc"]
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据:")
    print(df)
    print()
    
    # 应用数据清理
    validator = DataValidator()
    
    # 清理单数列
    for idx, value in df["单数"].items():
        is_valid, cleaned_value = validator.clean_and_validate_quantity(value)
        if is_valid:
            df.loc[idx, "单数"] = cleaned_value
    
    print("清理后数据:")
    print(df)
    print()
    
    # 验证结果
    expected_quantities = [123, 46, 100, 0, 0, 0]
    actual_quantities = df["单数"].tolist()
    
    if actual_quantities == expected_quantities:
        print("✅ DataFrame清理测试通过")
        return True
    else:
        print(f"❌ DataFrame清理测试失败")
        print(f"期望: {expected_quantities}")
        print(f"实际: {actual_quantities}")
        return False


def main():
    """主测试函数"""
    print("数据清理功能测试")
    print("=" * 60)
    
    test1_passed = test_quantity_cleaning()
    test2_passed = test_dataframe_cleaning()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！数据清理功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查数据清理逻辑。")


if __name__ == "__main__":
    main()

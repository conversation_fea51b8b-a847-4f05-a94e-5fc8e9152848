import xlwings as xw
import pandas as pd
from typing import List, Dict, Any, <PERSON>ple
import os
from dataclasses import dataclass
from PIL import Image
import io


@dataclass
class SheetData:
    """Sheet数据结构"""
    file_path: str
    sheet_name: str
    data: pd.DataFrame
    images: List[Dict[str, Any]]  # 图片信息
    

@dataclass
class ProcessedData:
    """处理后的数据结构"""
    keywords_summary: Dict[str, int]  # 关键词汇总 {关键词: 单数}
    original_data: List[SheetData]  # 原始数据


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self):
        self.required_columns = [
            "搜索主图", "商品ID", "搜索关键词", "单数", "合计单量", "需求备注"
        ]
    
    def read_excel_files(self, file_paths: List[str]) -> List[SheetData]:
        """读取多个Excel文件的所有sheet"""
        all_sheets_data = []
        
        for file_path in file_paths:
            sheets_data = self.read_single_excel_file(file_path)
            all_sheets_data.extend(sheets_data)
        
        return all_sheets_data
    
    def read_single_excel_file(self, file_path: str) -> List[SheetData]:
        """读取单个Excel文件的所有sheet"""
        sheets_data = []
        
        # 使用xlwings读取文件
        app = xw.App(visible=False)
        try:
            wb = app.books.open(file_path)
            
            for sheet in wb.sheets:
                sheet_data = self.read_sheet_data(file_path, sheet)
                if sheet_data:
                    sheets_data.append(sheet_data)
            
            wb.close()
        finally:
            app.quit()
        
        return sheets_data
    
    def read_sheet_data(self, file_path: str, sheet) -> SheetData:
        """读取单个sheet的数据"""
        try:
            # 读取数据范围 (假设数据从A1开始)
            used_range = sheet.used_range
            if not used_range:
                return None
            
            # 获取数据
            data_values = used_range.value
            if not data_values:
                return None
            
            # 转换为DataFrame
            if isinstance(data_values[0], list):
                df = pd.DataFrame(data_values[1:], columns=data_values[0])
            else:
                df = pd.DataFrame([data_values])
            
            # 验证必要列是否存在
            missing_columns = [col for col in self.required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告: Sheet '{sheet.name}' 缺少列: {missing_columns}")
                # 添加缺失的列
                for col in missing_columns:
                    df[col] = ""
            
            # 读取图片信息
            images = self.extract_images_from_sheet(sheet)
            
            return SheetData(
                file_path=file_path,
                sheet_name=sheet.name,
                data=df,
                images=images
            )
            
        except Exception as e:
            print(f"读取sheet '{sheet.name}' 时出错: {str(e)}")
            return None
    
    def extract_images_from_sheet(self, sheet) -> List[Dict[str, Any]]:
        """从sheet中提取图片信息"""
        images = []
        
        try:
            # 获取sheet中的所有图片
            for shape in sheet.shapes:
                if hasattr(shape, 'type') and 'picture' in str(shape.type).lower():
                    try:
                        # 获取图片位置信息
                        image_info = {
                            'name': shape.name,
                            'left': shape.left,
                            'top': shape.top,
                            'width': shape.width,
                            'height': shape.height,
                            'shape': shape  # 保存shape对象用于后续复制
                        }
                        images.append(image_info)
                    except Exception as e:
                        print(f"提取图片 {shape.name} 时出错: {str(e)}")
                        
        except Exception as e:
            print(f"提取图片时出错: {str(e)}")
        
        return images
    
    def validate_data(self, sheet_data: SheetData) -> bool:
        """验证数据有效性"""
        if sheet_data.data.empty:
            return False
        
        # 检查关键列是否有数据
        if "搜索关键词" in sheet_data.data.columns and "单数" in sheet_data.data.columns:
            # 至少有一行有效数据
            valid_rows = sheet_data.data[
                (sheet_data.data["搜索关键词"].notna()) & 
                (sheet_data.data["搜索关键词"] != "") &
                (sheet_data.data["单数"].notna())
            ]
            return len(valid_rows) > 0
        
        return False


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        pass
    
    def process_sheets_data(self, sheets_data: List[SheetData]) -> ProcessedData:
        """处理所有sheet数据"""
        keywords_summary = {}
        
        for sheet_data in sheets_data:
            # 汇总搜索关键词和单数
            self.summarize_keywords(sheet_data.data, keywords_summary)
        
        return ProcessedData(
            keywords_summary=keywords_summary,
            original_data=sheets_data
        )
    
    def summarize_keywords(self, df: pd.DataFrame, keywords_summary: Dict[str, int]):
        """汇总搜索关键词和单数"""
        if "搜索关键词" not in df.columns or "单数" not in df.columns:
            return
        
        for _, row in df.iterrows():
            keyword = str(row["搜索关键词"]).strip()
            if keyword and keyword != "nan" and keyword != "":
                try:
                    quantity = int(float(str(row["单数"])))
                    if keyword in keywords_summary:
                        keywords_summary[keyword] += quantity
                    else:
                        keywords_summary[keyword] = quantity
                except (ValueError, TypeError):
                    continue
    
    def split_data_by_customer_service(self, keywords_summary: Dict[str, int], 
                                     cs_data: List[Dict]) -> Dict[str, Dict[str, int]]:
        """根据客服配置拆分数据"""
        # 计算总数量
        total_quantity = sum(keywords_summary.values())
        total_cs_quantity = sum(cs["quantity"] for cs in cs_data)
        
        if total_cs_quantity == 0:
            raise ValueError("客服总分配数量不能为0")
        
        # 按比例分配
        cs_allocation = {}
        remaining_keywords = dict(keywords_summary)
        
        for cs in cs_data:
            cs_name = cs["name"]
            cs_quantity = cs["quantity"]
            cs_allocation[cs_name] = {}
            
            # 计算该客服应分配的比例
            ratio = cs_quantity / total_cs_quantity
            target_quantity = int(total_quantity * ratio)
            
            # 分配关键词
            allocated_quantity = 0
            keywords_to_remove = []
            
            for keyword, quantity in remaining_keywords.items():
                if allocated_quantity + quantity <= target_quantity:
                    cs_allocation[cs_name][keyword] = quantity
                    allocated_quantity += quantity
                    keywords_to_remove.append(keyword)
                elif allocated_quantity < target_quantity:
                    # 部分分配
                    needed = target_quantity - allocated_quantity
                    cs_allocation[cs_name][keyword] = needed
                    remaining_keywords[keyword] = quantity - needed
                    allocated_quantity = target_quantity
                    break
            
            # 移除已完全分配的关键词
            for keyword in keywords_to_remove:
                del remaining_keywords[keyword]
        
        # 将剩余的关键词分配给最后一个客服
        if remaining_keywords and cs_data:
            last_cs = cs_data[-1]["name"]
            for keyword, quantity in remaining_keywords.items():
                if keyword in cs_allocation[last_cs]:
                    cs_allocation[last_cs][keyword] += quantity
                else:
                    cs_allocation[last_cs][keyword] = quantity
        
        return cs_allocation


if __name__ == "__main__":
    # 测试代码
    reader = ExcelReader()
    processor = DataProcessor()
    
    # 示例用法
    file_paths = ["example1.xlsx", "example2.xlsx"]
    sheets_data = reader.read_excel_files(file_paths)
    processed_data = processor.process_sheets_data(sheets_data)
    
    print("关键词汇总:", processed_data.keywords_summary)

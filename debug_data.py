#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据调试工具
用于检查和分析Excel文件中的数据问题

使用方法:
python debug_data.py <excel_file_path>
"""

import sys
import os
import pandas as pd
import xlwings as xw
from excel_reader import ExcelReader
from data_processor import DataValidator


def analyze_excel_file(file_path: str):
    """分析Excel文件的数据"""
    print(f"正在分析文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return
    
    try:
        reader = ExcelReader()
        sheets_data = reader.read_single_excel_file(file_path)
        
        if not sheets_data:
            print("错误: 无法读取任何sheet数据")
            return
        
        print(f"找到 {len(sheets_data)} 个工作表:")
        
        for i, sheet_data in enumerate(sheets_data, 1):
            print(f"\n--- Sheet {i}: {sheet_data.sheet_name} ---")
            
            # 基本信息
            print(f"数据行数: {len(sheet_data.data)}")
            print(f"数据列数: {len(sheet_data.data.columns)}")
            print(f"列名: {list(sheet_data.data.columns)}")
            
            # 检查必要列
            required_columns = ["搜索关键词", "单数"]
            missing_columns = [col for col in required_columns if col not in sheet_data.data.columns]
            if missing_columns:
                print(f"⚠️  缺少必要列: {missing_columns}")
            else:
                print("✅ 包含所有必要列")
            
            # 分析搜索关键词列
            if "搜索关键词" in sheet_data.data.columns:
                keyword_col = sheet_data.data["搜索关键词"]
                valid_keywords = keyword_col.dropna()
                valid_keywords = valid_keywords[valid_keywords.astype(str).str.strip() != ""]
                valid_keywords = valid_keywords[valid_keywords.astype(str) != "nan"]
                
                print(f"搜索关键词:")
                print(f"  - 总行数: {len(keyword_col)}")
                print(f"  - 有效关键词: {len(valid_keywords)}")
                print(f"  - 空值/无效值: {len(keyword_col) - len(valid_keywords)}")
                
                if len(valid_keywords) > 0:
                    print(f"  - 示例关键词: {list(valid_keywords.head(3))}")
            
            # 分析单数列
            if "单数" in sheet_data.data.columns:
                quantity_col = sheet_data.data["单数"]
                print(f"单数列分析:")
                print(f"  - 总行数: {len(quantity_col)}")
                print(f"  - 数据类型分布:")
                
                # 统计不同类型的数据
                type_counts = {}
                valid_numbers = 0
                invalid_data = []
                
                for idx, value in quantity_col.items():
                    if pd.isna(value):
                        type_counts["空值"] = type_counts.get("空值", 0) + 1
                    else:
                        value_str = str(value).strip()
                        if value_str == "" or value_str == "nan":
                            type_counts["空字符串"] = type_counts.get("空字符串", 0) + 1
                        else:
                            # 尝试清理和验证
                            is_valid, cleaned_value = DataValidator.clean_and_validate_quantity(value)
                            if is_valid:
                                valid_numbers += 1
                                if cleaned_value != value:
                                    type_counts["需要清理的数字"] = type_counts.get("需要清理的数字", 0) + 1
                                else:
                                    type_counts["有效数字"] = type_counts.get("有效数字", 0) + 1
                            else:
                                type_counts["无效数据"] = type_counts.get("无效数据", 0) + 1
                                invalid_data.append((idx, value))
                
                for data_type, count in type_counts.items():
                    print(f"    {data_type}: {count}")
                
                print(f"  - 可转换为有效数字: {valid_numbers}")
                
                # 显示一些无效数据示例
                if invalid_data:
                    print(f"  - 无效数据示例:")
                    for idx, value in invalid_data[:5]:
                        print(f"    行{idx+1}: '{value}' (类型: {type(value)})")
                
                # 显示一些有效数据示例
                valid_samples = []
                for idx, value in quantity_col.items():
                    is_valid, cleaned_value = DataValidator.clean_and_validate_quantity(value)
                    if is_valid and cleaned_value > 0:
                        valid_samples.append((idx, value, cleaned_value))
                        if len(valid_samples) >= 5:
                            break
                
                if valid_samples:
                    print(f"  - 有效数据示例:")
                    for idx, original, cleaned in valid_samples:
                        if original != cleaned:
                            print(f"    行{idx+1}: '{original}' -> {cleaned}")
                        else:
                            print(f"    行{idx+1}: {original}")
            
            print()
    
    except Exception as e:
        print(f"分析文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python debug_data.py <excel_file_path>")
        print("示例: python debug_data.py data.xlsx")
        return
    
    file_path = sys.argv[1]
    analyze_excel_file(file_path)


if __name__ == "__main__":
    main()

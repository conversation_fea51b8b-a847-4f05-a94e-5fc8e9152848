import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, 
                             QProgressBar, QFileDialog, QGroupBox, QSpinBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont
import xlwings as xw
import pandas as pd
from typing import List, Dict, Any
import traceback


class ExcelProcessor(QThread):
    """Excel处理线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(bool, str)

    def __init__(self, input_files: List[str], output_dir: str, customer_service_data: List[Dict]):
        super().__init__()
        self.input_files = input_files
        self.output_dir = output_dir
        self.customer_service_data = customer_service_data

    def run(self):
        try:
            self.process_excel_files()
            self.finished_processing.emit(True, "处理完成！")
        except Exception as e:
            error_msg = f"处理过程中出现错误: {str(e)}\n{traceback.format_exc()}"
            self.finished_processing.emit(False, error_msg)

    def process_excel_files(self):
        """处理Excel文件的主要逻辑"""
        from excel_reader import ExcelReader, DataProcessor
        from data_processor import AdvancedDataProcessor, DataValidator
        from excel_writer import ExcelWriter

        # 初始化处理器
        reader = ExcelReader()
        basic_processor = DataProcessor()
        advanced_processor = AdvancedDataProcessor()
        validator = DataValidator()
        writer = ExcelWriter(self.output_dir)

        # 步骤1: 验证客服配置
        self.status_updated.emit("验证客服配置...")
        cs_errors = validator.validate_customer_service_config(self.customer_service_data)
        if cs_errors:
            raise ValueError(f"客服配置错误: {'; '.join(cs_errors)}")

        # 步骤2: 读取Excel文件
        self.status_updated.emit("读取Excel文件...")
        self.progress_updated.emit(10)

        sheets_data = reader.read_excel_files(self.input_files)

        # 步骤3: 清理和验证输入数据
        self.status_updated.emit("清理和验证输入数据...")
        data_errors = validator.validate_input_data(sheets_data)
        if data_errors:
            raise ValueError(f"输入数据错误: {'; '.join(data_errors)}")

        # 过滤掉空的sheet数据
        valid_sheets_data = [sheet for sheet in sheets_data if not sheet.data.empty]
        if not valid_sheets_data:
            raise ValueError("没有找到有效的数据表")

        self.status_updated.emit(f"成功加载 {len(valid_sheets_data)} 个有效数据表")
        self.progress_updated.emit(30)

        # 步骤4: 处理和汇总数据
        self.status_updated.emit("处理和汇总数据...")
        processed_data = basic_processor.process_sheets_data(valid_sheets_data)

        # 显示汇总信息
        total_keywords = len(processed_data.keywords_summary)
        total_quantity = sum(processed_data.keywords_summary.values())
        self.status_updated.emit(f"汇总完成: {total_keywords} 个关键词，总数量 {total_quantity}")

        if total_quantity == 0:
            raise ValueError("没有找到有效的数量数据，请检查Excel文件中的'单数'列")

        self.progress_updated.emit(50)

        # 步骤5: 优化分配算法
        self.status_updated.emit("计算客服数据分配...")
        cs_allocation = advanced_processor.optimize_allocation(
            processed_data.keywords_summary,
            self.customer_service_data
        )

        # 验证分配结果
        if not advanced_processor.validate_allocation(cs_allocation, processed_data.keywords_summary):
            raise ValueError("数据分配验证失败")

        self.progress_updated.emit(70)

        # 步骤6: 创建客服专用sheet
        self.status_updated.emit("创建客服专用数据...")
        cs_sheets = advanced_processor.create_customer_service_sheets(processed_data, cs_allocation)

        self.progress_updated.emit(80)

        # 步骤7: 写入Excel文件
        self.status_updated.emit("生成输出文件...")
        output_files = writer.write_customer_service_files(cs_sheets)

        # 步骤8: 创建汇总报告
        self.status_updated.emit("生成汇总报告...")
        summary_file = writer.create_summary_file(processed_data.keywords_summary, cs_allocation)

        self.progress_updated.emit(95)

        # 步骤9: 清理临时文件
        self.status_updated.emit("清理临时文件...")
        writer.cleanup_temp_files()

        self.progress_updated.emit(100)

        # 生成处理报告
        report = advanced_processor.generate_allocation_report(cs_allocation, self.customer_service_data)
        self.status_updated.emit(f"处理完成！生成了 {len(output_files)} 个客服文件")
        self.status_updated.emit(f"汇总报告: {summary_file}")
        self.status_updated.emit(report)


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.input_files = []
        self.customer_service_data = []
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("运营表格数据自动拆分程序")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 输入文件选择
        input_layout = QHBoxLayout()
        self.input_files_label = QLabel("未选择文件")
        self.select_input_btn = QPushButton("选择运营表格文件")
        self.select_input_btn.clicked.connect(self.select_input_files)
        input_layout.addWidget(QLabel("输入文件:"))
        input_layout.addWidget(self.input_files_label)
        input_layout.addWidget(self.select_input_btn)
        file_layout.addLayout(input_layout)
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.select_output_btn = QPushButton("选择输出目录")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(QLabel("输出目录:"))
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(self.select_output_btn)
        file_layout.addLayout(output_layout)
        
        main_layout.addWidget(file_group)
        
        # 客服配置区域
        cs_group = QGroupBox("客服配置")
        cs_layout = QVBoxLayout(cs_group)
        
        # 客服数量设置
        cs_count_layout = QHBoxLayout()
        self.cs_count_spinbox = QSpinBox()
        self.cs_count_spinbox.setMinimum(1)
        self.cs_count_spinbox.setMaximum(20)
        self.cs_count_spinbox.setValue(3)
        self.cs_count_spinbox.valueChanged.connect(self.update_cs_table)
        cs_count_layout.addWidget(QLabel("客服数量:"))
        cs_count_layout.addWidget(self.cs_count_spinbox)
        cs_count_layout.addStretch()
        cs_layout.addLayout(cs_count_layout)
        
        # 客服信息表格
        self.cs_table = QTableWidget()
        self.cs_table.setColumnCount(2)
        self.cs_table.setHorizontalHeaderLabels(["客服姓名", "分配数量"])
        self.cs_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        cs_layout.addWidget(self.cs_table)
        
        # 初始化客服表格
        self.update_cs_table()
        
        main_layout.addWidget(cs_group)
        
        # 处理控制区域
        control_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        control_layout.addWidget(self.process_btn)
        main_layout.addLayout(control_layout)
        
        # 进度显示区域
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.status_label = QLabel("就绪")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_group)
        
        # 日志显示区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
    
    def select_input_files(self):
        """选择输入文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择运营表格文件", "", 
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )
        if files:
            self.input_files = files
            file_names = [os.path.basename(f) for f in files]
            self.input_files_label.setText(f"已选择 {len(files)} 个文件: {', '.join(file_names[:3])}{'...' if len(files) > 3 else ''}")
    
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def update_cs_table(self):
        """更新客服表格"""
        count = self.cs_count_spinbox.value()
        self.cs_table.setRowCount(count)
        
        for i in range(count):
            # 客服姓名
            name_item = self.cs_table.item(i, 0)
            if not name_item:
                name_item = QTableWidgetItem(f"客服{i+1}")
                self.cs_table.setItem(i, 0, name_item)
            
            # 分配数量
            quantity_item = self.cs_table.item(i, 1)
            if not quantity_item:
                quantity_item = QTableWidgetItem("0")
                self.cs_table.setItem(i, 1, quantity_item)
    
    def get_customer_service_data(self) -> List[Dict]:
        """获取客服配置数据"""
        data = []
        for i in range(self.cs_table.rowCount()):
            name = self.cs_table.item(i, 0).text()
            try:
                quantity = int(self.cs_table.item(i, 1).text())
            except ValueError:
                quantity = 0
            data.append({"name": name, "quantity": quantity})
        return data
    
    def start_processing(self):
        """开始处理"""
        # 验证输入
        if not self.input_files:
            QMessageBox.warning(self, "警告", "请先选择输入文件！")
            return
        
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录！")
            return
        
        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "输出目录不存在！")
            return
        
        # 获取客服数据
        cs_data = self.get_customer_service_data()
        total_quantity = sum(cs["quantity"] for cs in cs_data)
        if total_quantity == 0:
            QMessageBox.warning(self, "警告", "请设置客服分配数量！")
            return
        
        # 禁用处理按钮
        self.process_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 启动处理线程
        self.processor = ExcelProcessor(self.input_files, output_dir, cs_data)
        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.status_label.setText)
        self.processor.status_updated.connect(self.log_text.append)
        self.processor.finished_processing.connect(self.on_processing_finished)
        self.processor.start()
    
    def on_processing_finished(self, success: bool, message: str):
        """处理完成回调"""
        self.process_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.log_text.append("✓ " + message)
        else:
            QMessageBox.critical(self, "错误", message)
            self.log_text.append("✗ " + message)
        
        self.status_label.setText("就绪")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

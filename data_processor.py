import pandas as pd
from typing import List, Dict, Any, Tuple
import math
from excel_reader import SheetData, ProcessedData


class AdvancedDataProcessor:
    """高级数据处理器"""
    
    def __init__(self):
        self.target_columns = ["搜索关键词", "单数"]  # C列和D列对应的列名
        self.target_range = (2, 10)  # C2-C10, D2-D10 对应的行范围
    
    def create_customer_service_sheets(self, processed_data: ProcessedData, 
                                     cs_allocation: Dict[str, Dict[str, int]]) -> Dict[str, List[SheetData]]:
        """为每个客服创建对应的sheet数据"""
        cs_sheets = {}
        
        for cs_name, cs_keywords in cs_allocation.items():
            cs_sheets[cs_name] = []
            
            # 为每个原始sheet创建对应的客服sheet
            for original_sheet in processed_data.original_data:
                cs_sheet = self.create_cs_sheet_from_original(
                    original_sheet, cs_keywords, cs_name
                )
                if cs_sheet:
                    cs_sheets[cs_name].append(cs_sheet)
        
        return cs_sheets
    
    def create_cs_sheet_from_original(self, original_sheet: SheetData, 
                                    cs_keywords: Dict[str, int], 
                                    cs_name: str) -> SheetData:
        """从原始sheet创建客服专用sheet"""
        # 复制原始数据
        new_data = original_sheet.data.copy()
        
        # 清空目标范围的数据
        self.clear_target_range(new_data)
        
        # 填充客服分配的数据
        self.fill_cs_data(new_data, cs_keywords)
        
        # 创建新的SheetData对象
        cs_sheet = SheetData(
            file_path=original_sheet.file_path,
            sheet_name=f"{original_sheet.sheet_name}_{cs_name}",
            data=new_data,
            images=original_sheet.images.copy()  # 复制图片信息
        )
        
        return cs_sheet
    
    def clear_target_range(self, df: pd.DataFrame):
        """清空目标范围(C2-C10, D2-D10)的数据"""
        start_row, end_row = self.target_range
        
        # 确保DataFrame有足够的行
        while len(df) < end_row:
            df.loc[len(df)] = ""
        
        # 清空指定范围
        for row_idx in range(start_row - 1, end_row):  # 转换为0-based索引
            if "搜索关键词" in df.columns:
                df.loc[row_idx, "搜索关键词"] = ""
            if "单数" in df.columns:
                df.loc[row_idx, "单数"] = ""
    
    def fill_cs_data(self, df: pd.DataFrame, cs_keywords: Dict[str, int]):
        """填充客服分配的数据到目标范围"""
        start_row, end_row = self.target_range
        available_rows = end_row - start_row + 1  # 可用行数
        
        # 确保列存在
        if "搜索关键词" not in df.columns:
            df["搜索关键词"] = ""
        if "单数" not in df.columns:
            df["单数"] = ""
        
        # 将关键词数据填入指定范围
        row_idx = start_row - 1  # 转换为0-based索引
        
        for keyword, quantity in cs_keywords.items():
            if row_idx >= start_row - 1 + available_rows:
                break  # 超出可用范围
            
            df.loc[row_idx, "搜索关键词"] = keyword
            df.loc[row_idx, "单数"] = quantity
            row_idx += 1
    
    def optimize_allocation(self, keywords_summary: Dict[str, int], 
                          cs_data: List[Dict]) -> Dict[str, Dict[str, int]]:
        """优化的分配算法"""
        # 按关键词数量排序（从大到小）
        sorted_keywords = sorted(keywords_summary.items(), key=lambda x: x[1], reverse=True)
        
        # 初始化客服分配
        cs_allocation = {cs["name"]: {} for cs in cs_data}
        cs_current_load = {cs["name"]: 0 for cs in cs_data}
        cs_target_load = {cs["name"]: cs["quantity"] for cs in cs_data}
        
        # 贪心算法分配
        for keyword, quantity in sorted_keywords:
            # 找到当前负载最小且还能容纳的客服
            best_cs = None
            min_load = float('inf')
            
            for cs_name, target_load in cs_target_load.items():
                current_load = cs_current_load[cs_name]
                if current_load + quantity <= target_load and current_load < min_load:
                    min_load = current_load
                    best_cs = cs_name
            
            # 如果没有找到合适的客服，分配给负载最小的
            if best_cs is None:
                best_cs = min(cs_current_load.keys(), key=lambda x: cs_current_load[x])
            
            # 分配关键词
            cs_allocation[best_cs][keyword] = quantity
            cs_current_load[best_cs] += quantity
        
        return cs_allocation
    
    def validate_allocation(self, cs_allocation: Dict[str, Dict[str, int]], 
                          original_summary: Dict[str, int]) -> bool:
        """验证分配结果的正确性"""
        # 检查总数是否匹配
        allocated_total = {}
        
        for cs_name, cs_keywords in cs_allocation.items():
            for keyword, quantity in cs_keywords.items():
                if keyword in allocated_total:
                    allocated_total[keyword] += quantity
                else:
                    allocated_total[keyword] = quantity
        
        # 比较原始数据和分配数据
        for keyword, original_quantity in original_summary.items():
            allocated_quantity = allocated_total.get(keyword, 0)
            if allocated_quantity != original_quantity:
                print(f"警告: 关键词 '{keyword}' 分配数量不匹配: 原始={original_quantity}, 分配={allocated_quantity}")
                return False
        
        return True
    
    def generate_allocation_report(self, cs_allocation: Dict[str, Dict[str, int]], 
                                 cs_data: List[Dict]) -> str:
        """生成分配报告"""
        report = "=== 数据分配报告 ===\n\n"
        
        for cs in cs_data:
            cs_name = cs["name"]
            target_quantity = cs["quantity"]
            
            if cs_name in cs_allocation:
                allocated_keywords = cs_allocation[cs_name]
                actual_quantity = sum(allocated_keywords.values())
                
                report += f"客服: {cs_name}\n"
                report += f"目标数量: {target_quantity}\n"
                report += f"实际分配: {actual_quantity}\n"
                report += f"分配关键词: {len(allocated_keywords)} 个\n"
                
                if allocated_keywords:
                    report += "详细分配:\n"
                    for keyword, quantity in allocated_keywords.items():
                        report += f"  - {keyword}: {quantity}\n"
                
                report += f"完成率: {actual_quantity/target_quantity*100:.1f}%\n\n"
        
        return report
    
    def split_large_quantities(self, keywords_summary: Dict[str, int], 
                             max_per_entry: int = 50) -> Dict[str, int]:
        """拆分大数量的关键词条目"""
        result = {}
        
        for keyword, quantity in keywords_summary.items():
            if quantity <= max_per_entry:
                result[keyword] = quantity
            else:
                # 拆分成多个条目
                num_splits = math.ceil(quantity / max_per_entry)
                base_quantity = quantity // num_splits
                remainder = quantity % num_splits
                
                for i in range(num_splits):
                    split_keyword = f"{keyword}_part{i+1}"
                    split_quantity = base_quantity + (1 if i < remainder else 0)
                    result[split_keyword] = split_quantity
        
        return result


class DataValidator:
    """数据验证器"""

    @staticmethod
    def clean_and_validate_quantity(value) -> tuple:
        """清理和验证单数数据，返回(是否有效, 清理后的值)"""
        if pd.isna(value) or value == "":
            return True, 0  # 空值视为0

        # 转换为字符串并清理
        str_value = str(value).strip()

        # 移除常见的非数字字符
        import re
        # 保留数字、小数点、负号、E(科学计数法)
        cleaned = re.sub(r'[^\d.E+-]', '', str_value.upper())

        if not cleaned:
            return True, 0  # 清理后为空，视为0

        try:
            # 尝试转换为浮点数
            float_value = float(cleaned)
            # 转换为整数（四舍五入）
            int_value = round(float_value)
            # 确保非负
            final_value = max(0, int_value)
            return True, final_value
        except (ValueError, TypeError):
            return True, 0  # 无法转换的值设为0，而不是返回False

    @staticmethod
    def validate_input_data(sheets_data: List[SheetData]) -> List[str]:
        """验证输入数据"""
        errors = []
        warnings = []

        if not sheets_data:
            errors.append("没有找到有效的sheet数据")
            return errors

        for sheet_data in sheets_data:
            # 检查必要列
            required_columns = ["搜索关键词", "单数"]
            missing_columns = [col for col in required_columns if col not in sheet_data.data.columns]

            if missing_columns:
                errors.append(f"Sheet '{sheet_data.sheet_name}' 缺少必要列: {missing_columns}")
                continue

            # 检查数据有效性
            if sheet_data.data.empty:
                warnings.append(f"Sheet '{sheet_data.sheet_name}' 没有数据，将跳过")
                continue

            # 清理和验证单数列
            if "单数" in sheet_data.data.columns:
                invalid_count = 0
                cleaned_count = 0

                for idx, value in sheet_data.data["单数"].items():
                    is_valid, cleaned_value = DataValidator.clean_and_validate_quantity(value)
                    if is_valid:
                        if cleaned_value != value:
                            sheet_data.data.loc[idx, "单数"] = cleaned_value
                            cleaned_count += 1
                    else:
                        invalid_count += 1
                        # 将无效值设为0
                        sheet_data.data.loc[idx, "单数"] = 0
                        cleaned_count += 1

                if cleaned_count > 0:
                    warnings.append(f"Sheet '{sheet_data.sheet_name}' 清理了 {cleaned_count} 个单数数据")

            # 检查是否有有效的关键词数据
            valid_keywords = sheet_data.data[
                (sheet_data.data["搜索关键词"].notna()) &
                (sheet_data.data["搜索关键词"].astype(str).str.strip() != "") &
                (sheet_data.data["搜索关键词"].astype(str) != "nan")
            ]

            if valid_keywords.empty:
                warnings.append(f"Sheet '{sheet_data.sheet_name}' 没有有效的搜索关键词数据，将跳过")

        # 将警告信息打印到控制台，但不作为错误返回
        for warning in warnings:
            print(f"警告: {warning}")

        return errors
    
    @staticmethod
    def validate_customer_service_config(cs_data: List[Dict]) -> List[str]:
        """验证客服配置"""
        errors = []
        
        if not cs_data:
            errors.append("没有配置客服信息")
            return errors
        
        total_quantity = sum(cs.get("quantity", 0) for cs in cs_data)
        if total_quantity <= 0:
            errors.append("客服总分配数量必须大于0")
        
        for i, cs in enumerate(cs_data):
            if not cs.get("name", "").strip():
                errors.append(f"客服 {i+1} 的姓名不能为空")
            
            if cs.get("quantity", 0) <= 0:
                errors.append(f"客服 '{cs.get('name', f'客服{i+1}')}' 的分配数量必须大于0")
        
        return errors

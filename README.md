# 运营表格数据自动拆分程序

## 功能概述

这是一个基于PyQt6和xlwings开发的自动化工具，用于处理运营表格数据的拆分和分配。

### 主要功能

1. **多文件多Sheet处理**: 支持读取多个Excel文件，每个文件可包含多个工作表
2. **数据汇总**: 自动汇总搜索关键词和对应的单数
3. **智能分配**: 根据客服配置自动将数据分配给不同客服
4. **精确修改**: 只修改指定范围(C2-C10和D2-D10)的内容，保持其他数据不变
5. **图片复制**: 自动复制原表格中的图片到新生成的表格
6. **报告生成**: 生成详细的分配报告和汇总统计

## 系统要求

### 软件环境
- Windows 10/11 (推荐)
- Python 3.8+ 
- Microsoft Excel 2016+ (必须安装)

### Python依赖包
```
PyQt6==6.6.1
xlwings==0.31.4
pandas==2.1.4
openpyxl==3.1.2
Pillow==10.1.0
```

## 安装步骤

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 确保Excel可用
- 确保已安装Microsoft Excel
- 关闭所有Excel窗口
- 确保Excel没有被其他程序占用

### 3. 运行程序
```bash
python main.py
```

## 使用说明

### 1. 启动程序
双击运行`main.py`或在命令行中执行`python main.py`

### 2. 选择输入文件
- 点击"选择运营表格文件"按钮
- 选择一个或多个Excel文件(.xlsx或.xls格式)
- 程序会自动处理所有选中文件的所有工作表

### 3. 设置输出目录
- 点击"选择输出目录"按钮
- 选择处理结果的保存位置

### 4. 配置客服信息
- 设置客服数量(1-20人)
- 为每个客服设置姓名和分配数量
- 分配数量表示该客服应处理的数据量

### 5. 开始处理
- 点击"开始处理"按钮
- 程序会显示处理进度和状态
- 处理完成后会显示结果报告

## 输入文件格式要求

### 必需列
输入的Excel文件必须包含以下列：
- **搜索主图**: 商品主图
- **商品ID**: 商品唯一标识
- **搜索关键词**: 用于搜索的关键词
- **单数**: 对应的数量
- **合计单量**: 总计数量
- **需求备注**: 备注信息

### 数据格式
- 搜索关键词: 文本格式，不能为空
- 单数: 数字格式，必须为正整数
- 其他列: 可以为任意格式

## 输出文件说明

### 客服专用文件
- 文件名格式: `{客服姓名}_运营表格_{时间戳}.xlsx`
- 每个客服一个文件，包含分配给该客服的所有数据
- 保持原始表格格式，只修改C2-C10和D2-D10范围的内容
- 自动复制原表格中的图片

### 汇总报告文件
- 文件名格式: `数据汇总报告_{时间戳}.xlsx`
- 包含两个工作表:
  - **关键词汇总**: 所有关键词及其总数量
  - **客服分配汇总**: 每个客服的详细分配情况

## 处理逻辑说明

### 数据汇总
1. 读取所有输入文件的所有工作表
2. 提取"搜索关键词"和"单数"列的数据
3. 按关键词汇总，计算每个关键词的总数量

### 智能分配算法
1. 根据客服配置的分配数量计算比例
2. 使用优化算法将关键词分配给各个客服
3. 确保分配结果尽可能接近设定的比例
4. 验证分配结果的正确性

### 数据写入
1. 为每个客服创建独立的Excel文件
2. 复制原始表格的格式和结构
3. 只修改C2-C10和D2-D10范围的内容
4. 复制原表格中的图片到对应位置

## 注意事项

### 使用前准备
1. **关闭Excel**: 处理前请关闭所有Excel窗口
2. **备份数据**: 建议备份原始数据文件
3. **检查格式**: 确保输入文件包含必需的列

### 处理过程中
1. **不要操作Excel**: 处理过程中请勿手动打开或操作Excel
2. **等待完成**: 大文件处理可能需要较长时间，请耐心等待
3. **查看日志**: 关注程序界面的状态信息和日志

### 处理完成后
1. **检查结果**: 验证生成的文件是否符合预期
2. **查看报告**: 阅读汇总报告了解分配情况
3. **清理文件**: 可以删除不需要的临时文件

## 常见问题

### Q: 程序启动时提示"无法连接到Excel"
A: 请确保已安装Microsoft Excel，并关闭所有Excel窗口后重试。

### Q: 处理过程中程序卡住
A: 可能是Excel进程异常，请结束所有Excel进程后重新启动程序。

### Q: 生成的文件中图片丢失
A: 图片复制功能可能受Excel版本影响，建议使用较新版本的Excel。

### Q: 分配结果不均匀
A: 程序使用智能算法尽可能均匀分配，但会优先保证数据完整性。

### Q: 提示"包含无效的单数数据"
A: 程序已自动清理数据格式问题。如果仍有问题，请检查：
1. "单数"列是否包含数字数据
2. 使用调试工具分析数据：`python debug_data.py your_file.xlsx`
3. 确保数据不是纯文本格式

## 故障排除

### 数据格式问题
如果遇到数据格式错误，可以使用调试工具分析：

```bash
python debug_data.py your_excel_file.xlsx
```

这个工具会显示：
- 每个工作表的基本信息
- 数据类型分布
- 无效数据示例
- 数据清理建议

### 数据清理规则
程序会自动清理以下数据问题：
1. **空值处理**: 空值或空字符串转换为0
2. **格式清理**: 移除非数字字符（如货币符号、单位等）
3. **类型转换**: 自动转换为整数
4. **负数处理**: 负数转换为0
5. **小数处理**: 小数四舍五入为整数

### 支持的数据格式示例
- ✅ 纯数字: `123`, `45`
- ✅ 带小数: `12.5`, `3.14`
- ✅ 带符号: `+10`, `-5` (负数会转为0)
- ✅ 带单位: `100个`, `50件`
- ✅ 带货币: `￥100`, `$50`
- ✅ 科学计数法: `1.23E+02`

## 技术支持

如遇到问题，请提供以下信息：
1. 错误信息截图
2. 输入文件格式说明
3. 操作系统和Excel版本
4. 程序日志内容

## 版本历史

### v1.0.0 (2025-08-06)
- 初始版本发布
- 支持多文件多Sheet处理
- 实现智能数据分配算法
- 支持图片复制功能
- 生成详细报告
